<script setup lang="ts">
const { t } = useI18n()
const productStore = useProductStore()
const { oneUSDCreditsWithPromotion } = storeToRefs(productStore)
const creditsStore = useCreditsStore()
const { authorize } = useAuthorize()
// Pricing Calculator state
const calculatorMode = ref('budget') // budget | resources
const budgetAmount = ref(10) // minimum $10
const resourceCounts = ref<Record<string, number>>({
  'imagen-flash': 0,
  'imagen-4-fast': 0,
  'imagen-4': 0,
  'imagen-4-ultra': 0,
  'veo-2': 0,
  'veo-3-fast': 0,
  'veo-3': 0,
  'tts-flash': 0,
  'tts-pro': 0,
  'dialogue-flash': 0,
  'dialogue-pro': 0
})

// Video resolution selection
const videoResolution = ref<Record<string, string>>({
  'veo-2': '720p',
  'veo-3-fast': '720p',
  'veo-3': '720p'
})

// Function to calculate video pricing based on resolution
const getVideoPricing = (service: any, resolution: string = '720p') => {
  if (!service.hasResolutionPricing || resolution === '720p') {
    return {
      credits: service.credits,
      priceUnit: service.priceUnit
    }
  }

  // Full HD pricing
  if (resolution === '1080p' && service.fullHDSurcharge) {
    return {
      credits: service.credits + service.fullHDSurcharge,
      priceUnit: service.priceUnit + (service.fullHDSurcharge / 100) // Convert credits to USD
    }
  }

  return {
    credits: service.credits,
    priceUnit: service.priceUnit
  }
}

// Calculator mode items
const calculatorModeItems = computed(() => {
  return [
    {
      label: t('Budget Calculator'),
      value: 'budget',
      icon: 'solar:dollar-bold'
    },
    {
      label: t('Resource Calculator'),
      value: 'resources',
      icon: 'solar:gallery-bold'
    }
  ]
})

// Service pricing data
const servicePricing = computed(() => {
  return [
    {
      id: 'imagen-flash',
      name: t('Nano Banana (Gemini 2.5 Flash)'),
      type: 'image',
      icon: 'i-lucide-zap',
      iconColor: 'text-yellow-500',
      priceUnit: 0.04,
      discount: 1,
      credits: 20 // Fixed price for imagen-flash
    },
    {
      id: 'imagen-4-fast',
      name: t('Imagen 4 Fast'),
      type: 'image',
      icon: 'i-lucide-image',
      iconColor: 'text-blue-500',
      priceUnit: 0.02,
      discount: 0.4,
      credits: 10 // Fixed price for imagen-4-fast
    },
    {
      id: 'imagen-4',
      name: t('Imagen 4'),
      type: 'image',
      icon: 'i-lucide-image-plus',
      iconColor: 'text-green-500',
      priceUnit: 0.04,
      discount: 0.4,
      credits: 20 // Fixed price for imagen-4
    },
    {
      id: 'imagen-4-ultra',
      name: t('Imagen 4 Ultra'),
      type: 'image',
      icon: 'i-lucide-crown',
      iconColor: 'text-purple-500',
      priceUnit: 0.06,
      discount: 0.4,
      credits: 30 // Fixed price for imagen-4-ultra
    },
    {
      id: 'veo-2',
      name: t('Veo 2'),
      type: 'video',
      icon: 'i-lucide-video',
      iconColor: 'text-red-500',
      priceUnit: 4, // $4.00 per video original price (800 credits)
      discount: 0.025, // ~97% discount (20/800 = 0.025)
      credits: 20, // Discounted 20 credits per video
      description: t('videoGen.fixedPricePerVideo'),
      hasResolutionPricing: false
    },
    {
      id: 'veo-3-fast',
      name: t('Veo 3 Fast (With Audio)'),
      type: 'video',
      icon: 'i-lucide-play-circle',
      iconColor: 'text-orange-500',
      priceUnit: 6, // $6.00 original price for 720p (1200 credits)
      discount: 0.083, // ~91% discount (100/1200 = 0.083)
      credits: 100, // Discounted 100 credits for 720p
      description: t('videoGen.pricingWithResolution'),
      hasResolutionPricing: true,
      fullHDSurcharge: -30 // Reduce to 70 credits for Full HD (70-100 = -30)
    },
    {
      id: 'veo-3',
      name: t('Veo 3'),
      type: 'video',
      icon: 'i-lucide-film',
      iconColor: 'text-indigo-500',
      priceUnit: 3.2, // $3.20 original price for 720p (640 credits)
      discount: 0.03125, // ~96% discount (20/640 = 0.03125)
      credits: 20, // Discounted 20 credits for 720p
      description: t('videoGen.pricingWithResolution'),
      hasResolutionPricing: true,
      fullHDSurcharge: 180 // Increase to 200 credits for Full HD (200-20 = 180)
    },
    {
      id: 'tts-flash',
      name: t('Gemini 2.5 Flash TTS'),
      type: 'speech',
      icon: 'i-lucide-mic',
      iconColor: 'text-emerald-500',
      priceUnit: 0.00002, // $20 per 1M characters = $0.00002 per character
      discount: 1, // No discount
      credits: 1 // Fixed price for tts-flash
    },
    {
      id: 'tts-pro',
      name: t('Gemini 2.5 Pro TTS'),
      type: 'speech',
      icon: 'i-lucide-mic-2',
      iconColor: 'text-teal-500',
      priceUnit: 0.00004, // $40 per 1M characters = $0.00004 per character
      discount: 1, // No discount
      credits: 2 // Fixed price for tts-pro
    },
    {
      id: 'dialogue-flash',
      name: t('Gemini 2.5 Flash Dialogue'),
      type: 'dialogue',
      icon: 'i-lucide-users',
      iconColor: 'text-purple-500',
      priceUnit: 0.00002, // Same as TTS Flash: $20 per 1M characters = $0.00002 per character
      discount: 1, // No discount
      credits: 1 // Fixed price for dialogue-flash
    },
    {
      id: 'dialogue-pro',
      name: t('Gemini 2.5 Pro Dialogue'),
      type: 'dialogue',
      icon: 'i-lucide-message-circle',
      iconColor: 'text-pink-500',
      priceUnit: 0.00004, // Same as TTS Pro: $40 per 1M characters = $0.00004 per character
      discount: 1, // No discount
      credits: 2 // Fixed price for dialogue-pro
    }
  ]
})

// Calculate resources from budget
const calculatedResources = computed(() => {
  const budget = budgetAmount.value
  const totalCredits = budget * oneUSDCreditsWithPromotion.value

  return servicePricing.value.map((service) => {
    let maxResources, actualPrice

    if (service.type === 'video') {
      const resolution = videoResolution.value[service.id] || '720p'
      const pricing = getVideoPricing(service, resolution)
      maxResources = Math.floor(totalCredits / pricing.credits)
      actualPrice = parseFloat((pricing.priceUnit * service.discount).toFixed(4))
    } else {
      maxResources = Math.floor(totalCredits / service.credits)
      actualPrice = parseFloat((service.priceUnit * service.discount).toFixed(4))
    }

    return {
      ...service,
      maxResources,
      actualPrice
    }
  })
})

// Calculate total price from resources
const calculatedPrice = computed(() => {
  let totalPrice = 0

  servicePricing.value.forEach((service) => {
    const count = resourceCounts.value[service.id] || 0
    if (service.type === 'video') {
      const resolution = videoResolution.value[service.id] || '720p'
      const pricing = getVideoPricing(service, resolution)
      totalPrice += count * pricing.priceUnit * service.discount
    } else {
      totalPrice += count * service.priceUnit * service.discount
    }
  })

  return totalPrice
})

// Check if purchase amount meets minimum requirement and is divisible by 10
const canPurchase = computed(() => {
  if (calculatorMode.value === 'budget') {
    return budgetAmount.value >= 10 && budgetAmount.value % 10 === 0
  } else {
    return calculatedPrice.value >= 10 && calculatedPrice.value % 10 === 0
  }
})

// Removed unused purchaseAmount computed

// Functions
const handleBuyNow = () => {
  let creditsNeeded = 0
  let totalPrice = 0

  if (calculatorMode.value === 'budget') {
    totalPrice = budgetAmount.value
    creditsNeeded = budgetAmount.value * oneUSDCreditsWithPromotion.value
  } else {
    totalPrice = calculatedPrice.value
    servicePricing.value.forEach((service) => {
      const count = resourceCounts.value[service.id] || 0
      if (service.type === 'video') {
        const resolution = videoResolution.value[service.id] || '720p'
        const pricing = getVideoPricing(service, resolution)
        creditsNeeded += count * pricing.credits
      } else {
        creditsNeeded += count * service.credits
      }
    })
  }

  // Check minimum purchase amount
  if (totalPrice < 10) {
    const toast = useToast()
    toast.add({
      id: 'minimum-purchase-error',
      title: t('Minimum Purchase Required'),
      description: t(
        'Minimum purchase amount is $10. Please increase your selection.'
      ),
      color: 'error'
    })
    return
  }

  // Check if amount is divisible by 10
  if (totalPrice % 10 !== 0) {
    const toast = useToast()
    toast.add({
      id: 'invalid-amount-error',
      title: t('Invalid Purchase Amount'),
      description: t(
        'Purchase amount must be a multiple of $10 (e.g., $10, $20, $30...).'
      ),
      color: 'error'
    })
    return
  }

  if (creditsNeeded > 0) {
    creditsStore.processBuyCredits(creditsNeeded)
  }
}

const resetResourceCounts = () => {
  Object.keys(resourceCounts.value).forEach((key) => {
    resourceCounts.value[key] = 0
  })
}

// Helper function to format price
const formatPrice = (price: number) => {
  return price.toFixed(4).replace(/\.?0+$/, '')
}

// Removed formatDuration as it's no longer used

// Get quick values for sliders
const getQuickValues = (type: string) => {
  if (type === 'image') {
    return [
      { value: 100, label: '100' },
      { value: 1000, label: '1K' },
      { value: 10000, label: '10K' },
      { value: 50000, label: '50K' },
      { value: 100000, label: '100K' }
    ]
  } else if (type === 'video') {
    // Video quick values in number of videos
    return [
      { value: 1, label: '1' },
      { value: 5, label: '5' },
      { value: 10, label: '10' },
      { value: 25, label: '25' },
      { value: 50, label: '50' },
      { value: 100, label: '100' }
    ]
  } else if (type === 'speech') {
    // Speech quick values in characters
    return [
      { value: 1000, label: '1K' },
      { value: 5000, label: '5K' },
      { value: 10000, label: '10K' },
      { value: 50000, label: '50K' },
      { value: 100000, label: '100K' },
      { value: 1000000, label: '1M' }
    ]
  } else if (type === 'dialogue') {
    // Dialogue quick values in characters (similar to speech but focused on conversation length)
    return [
      { value: 500, label: '500' },
      { value: 2000, label: '2K' },
      { value: 5000, label: '5K' },
      { value: 10000, label: '10K' },
      { value: 25000, label: '25K' },
      { value: 50000, label: '50K' }
    ]
  } else {
    return []
  }
}
</script>

<template>
  <div>
    <div class="mb-4">
      <h2 class="text-xl font-bold">
        {{ $t("Pricing Calculator") }}
      </h2>
      <p class="text-sm text-muted">
        {{
          $t("Calculate how many resources can you generate with your budget.")
        }}
      </p>
    </div>
    <UPricingPlan
      :price="
        calculatorMode === 'budget'
          ? `$${budgetAmount}`
          : `$${calculatedPrice.toFixed(2)}`
      "
      :button="{
        label: canPurchase ? $t('Buy now') : $t('Minimum $10 required'),
        onClick: () => authorize(handleBuyNow, 'any', 0),
        disabled: !canPurchase,
        color: canPurchase ? 'primary' : 'neutral'
      }"
      orientation="horizontal"
      variant="outline"
    >
      <template #body>
        <div class="space-y-6 w-full flex-1">
          <!-- Calculator Mode Toggle -->
          <div class="flex justify-center">
            <UTabs
              v-model="calculatorMode"
              color="primary"
              :content="false"
              :items="calculatorModeItems"
              class="w-fit"
              size="sm"
            />
          </div>

          <!-- Budget Calculator Mode -->
          <div
            v-if="calculatorMode === 'budget'"
            class="space-y-4"
          >
            <UFormField
              :label="$t('Budget Amount')"
              class="w-full"
            >
              <div class="space-y-3">
                <!-- Budget Input Field -->
                <div class="flex items-center gap-3">
                  <div class="flex-1">
                    <UInputNumber
                      v-model="budgetAmount"
                      :min="10"
                      :max="100000"
                      :step="10"
                      size="lg"
                      class="w-full"
                      :placeholder="$t('Enter budget amount')"
                    >
                      <template #leading>
                        <span class="text-gray-500">$</span>
                      </template>
                    </UInputNumber>
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ $t("Min: $10") }}
                  </div>
                </div>

                <!-- Slider -->
                <USlider
                  v-model="budgetAmount"
                  :min="10"
                  :max="10000"
                  :step="10"
                  class="w-full"
                />

                <!-- Quick Budget Values -->
                <div
                  class="flex justify-between items-center text-sm text-gray-500"
                >
                  <span>$10</span>
                  <div class="flex gap-2">
                    <button
                      v-for="quickBudget in [50, 100, 500, 1000, 5000]"
                      :key="quickBudget"
                      class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      @click="budgetAmount = quickBudget"
                    >
                      ${{ formatNumber(quickBudget) }}
                    </button>
                  </div>
                  <span>$10,000</span>
                </div>
              </div>
            </UFormField>

            <!-- Video Resolution Settings -->
            <div class="space-y-3">
              <div>
                <h4 class="font-semibold text-sm">
                  {{ $t("Video Resolution Settings") }}
                </h4>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {{ $t("Choose resolution for video generation pricing") }}
                </p>
              </div>
              <div class="grid grid-cols-1 gap-3">
                <div
                  v-for="service in servicePricing.filter(s => s.type === 'video' && s.hasResolutionPricing)"
                  :key="service.id"
                  class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3"
                >
                  <UFormField
                    :label="service.name"
                    class="w-full"
                  >
                    <USelectMenu
                      v-model="videoResolution[service.id]"
                      :options="[
                        { label: '720p HD - Base Price', value: '720p' },
                        { label: `1080p Full HD - +${service.fullHDSurcharge} credits`, value: '1080p' }
                      ]"
                      option-attribute="label"
                      value-attribute="value"
                      size="sm"
                      class="w-full"
                    />
                  </UFormField>
                </div>
              </div>
            </div>

            <!-- Resources Display -->
            <div class="space-y-3">
              <div>
                <h4 class="font-semibold text-sm">
                  {{ $t("Resources you can generate:") }}
                </h4>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {{
                    $t(
                      "Each amount shows what you can generate with your entire budget (choose one type). Video generation is priced per video, speech and dialogue generation in characters."
                    )
                  }}
                </p>
              </div>
              <div class="space-y-3">
                <div
                  v-for="(resource, index) in calculatedResources"
                  :key="resource.id"
                  class="relative"
                >
                  <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                    <div class="flex justify-between items-center">
                      <div class="flex items-center gap-3">
                        <div class="flex-shrink-0">
                          <UIcon
                            :name="resource.icon"
                            :class="['size-5', resource.iconColor]"
                          />
                        </div>
                        <div>
                          <div class="font-medium text-sm">
                            {{ resource.name }}
                            <span
                              v-if="resource.type === 'video' && resource.hasResolutionPricing"
                              class="text-xs text-gray-400"
                            >
                              ({{ videoResolution[resource.id] }})
                            </span>
                          </div>
                          <div class="text-xs text-gray-500">
                            ${{ formatPrice(resource.actualPrice) }}
                          </div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="font-bold text-primary">
                          {{ formatNumber(resource.maxResources) }}
                        </div>
                        <div class="text-xs text-gray-500">
                          {{
                            resource.type === "image"
                              ? $t("Images")
                              : resource.type === "video"
                                ? $t("Videos")
                                : resource.type === "speech"
                                  ? $t("Characters")
                                  : $t("Characters")
                          }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- OR separator (except for last item) -->
                  <div
                    v-if="index < calculatedResources.length - 1"
                    class="flex justify-center items-center my-2"
                  >
                    <div class="flex items-center gap-2 text-xs text-gray-500">
                      <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1" />
                      <span
                        class="px-2 bg-white dark:bg-gray-900 font-medium"
                      >{{ $t("OR") }}</span>
                      <div class="h-px bg-gray-300 dark:bg-gray-600 flex-1" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Resource Calculator Mode -->
          <div
            v-else
            class="space-y-4"
          >
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <h4 class="font-semibold text-sm">
                  {{ $t("Select resources you want:") }}
                </h4>
                <UButton
                  size="xs"
                  variant="ghost"
                  color="neutral"
                  @click="resetResourceCounts"
                >
                  {{ $t("Reset") }}
                </UButton>
              </div>
              <p class="text-xs text-gray-600 dark:text-gray-400">
                {{ $t("For video generation, enter duration in seconds. For speech and dialogue generation, enter number of characters. Use quick buttons for common values.") }}
              </p>
            </div>

            <div class="space-y-4">
              <div
                v-for="service in servicePricing"
                :key="service.id"
                class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4"
              >
                <div class="flex justify-between items-center mb-2">
                  <div class="flex items-center gap-3">
                    <div class="flex-shrink-0">
                      <UIcon
                        :name="service.icon"
                        :class="['size-5', service.iconColor]"
                      />
                    </div>
                    <div>
                      <div class="font-medium text-sm">
                        {{ service.name }}
                      </div>
                      <div class="text-xs text-gray-500">
                        <template v-if="service.type === 'video'">
                          <span v-if="service.hasResolutionPricing">
                            {{ getVideoPricing(service, videoResolution[service.id]).credits }} {{ $t("credits") }} / {{ $t("video") }}
                            <span class="text-gray-400">({{ videoResolution[service.id] }})</span>
                          </span>
                          <span v-else>
                            {{ service.credits }} {{ $t("credits") }} / {{ $t("video") }}
                          </span>
                        </template>
                        <template v-else>
                          {{ service.credits }} {{ $t("credits") }} /
                          {{
                            service.type === "image"
                              ? $t("image")
                              : service.type === "speech"
                                ? $t("character")
                                : $t("character")
                          }}
                        </template>
                      </div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-bold text-primary">
                      <template v-if="service.type === 'video' && service.hasResolutionPricing">
                        ${{ formatPrice(getVideoPricing(service, videoResolution[service.id]).priceUnit * service.discount) }}
                      </template>
                      <template v-else>
                        ${{ formatPrice(service.priceUnit * service.discount) }}
                      </template>
                    </div>
                    <div class="text-xs text-gray-500">
                      {{
                        service.type === "image"
                          ? $t("per image")
                          : service.type === "video"
                            ? $t("per video")
                            : service.type === "speech"
                              ? $t("per character")
                              : $t("per character")
                      }}
                    </div>
                  </div>
                </div>

                <!-- Resolution Selector for Video Services -->
                <div
                  v-if="service.type === 'video' && service.hasResolutionPricing"
                  class="mb-3"
                >
                  <UFormField
                    :label="$t('Resolution')"
                    class="w-full"
                  >
                    <USelectMenu
                      v-model="videoResolution[service.id]"
                      :options="[
                        { label: '720p HD - Base Price', value: '720p' },
                        { label: `1080p Full HD - +${service.fullHDSurcharge} credits`, value: '1080p' }
                      ]"
                      option-attribute="label"
                      value-attribute="value"
                      size="sm"
                      class="w-full"
                    />
                  </UFormField>
                </div>

                <UFormField
                  :label="service.type === 'image'
                    ? `${$t('Quantity')}: ${formatNumber(resourceCounts[service.id] || 0)}`
                    : service.type === 'video'
                      ? `${$t('Videos')}: ${formatNumber(resourceCounts[service.id] || 0)}`
                      : service.type === 'speech'
                        ? `${$t('Characters')}: ${formatNumber(resourceCounts[service.id] || 0)}`
                        : `${$t('Dialogue Characters')}: ${formatNumber(resourceCounts[service.id] || 0)}`"
                  class="w-full"
                >
                  <div class="space-y-3">
                    <!-- Slider -->
                    <USlider
                      v-model="resourceCounts[service.id]"
                      :min="0"
                      :max="1000000"
                      :step="1"
                      class="w-full"
                    />

                    <!-- Quick values -->
                    <div class="flex justify-between text-xs text-gray-500">
                      <span>{{
                        service.type === 'image'
                          ? '0'
                          : service.type === 'video'
                            ? '0 videos'
                            : service.type === 'speech'
                              ? '0 chars'
                              : '0 chars'
                      }}</span>
                      <div class="flex gap-2">
                        <button
                          v-for="quickValue in getQuickValues(service.type)"
                          :key="typeof quickValue === 'object' ? quickValue.value : quickValue"
                          class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                          @click="resourceCounts[service.id] = typeof quickValue === 'object' ? quickValue.value : quickValue"
                        >
                          {{ typeof quickValue === 'object' ? quickValue.label : formatNumber(quickValue) }}
                        </button>
                      </div>
                      <span>{{
                        service.type === 'image'
                          ? formatNumber(1000000)
                          : service.type === 'video'
                            ? '1M videos'
                            : service.type === 'speech'
                              ? '1M chars'
                              : '50K chars'
                      }}</span>
                    </div>

                    <!-- Input Number for precise control -->
                    <UInputNumber
                      v-model="resourceCounts[service.id]"
                      :min="0"
                      :max="1000000"
                      :step="1"
                      size="sm"
                      class="w-full"
                      :placeholder="service.type === 'image'
                        ? $t('Enter exact number')
                        : service.type === 'video'
                          ? $t('Enter number of videos')
                          : service.type === 'speech'
                            ? $t('Enter number of characters')
                            : $t('Enter dialogue characters')"
                    />
                  </div>
                </UFormField>
              </div>
            </div>

            <!-- Total Display -->
            <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
              <div class="flex justify-between items-center">
                <span class="font-semibold">{{ $t("Total Cost:") }}</span>
                <span class="text-lg sm:text-xl font-bold text-primary">${{ calculatedPrice.toFixed(2) }}</span>
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{
                  $t("Approximately {credits} credits", {
                    credits: formatNumber(Math.round(calculatedPrice * oneUSDCreditsWithPromotion))
                  })
                }}
              </div>

              <!-- Minimum Purchase Warning -->
              <div
                v-if="!canPurchase && calculatedPrice > 0"
                class="mt-3 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg"
              >
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-lucide-alert-triangle"
                    class="size-4 text-amber-600 dark:text-amber-400"
                  />
                  <span
                    class="text-sm text-amber-800 dark:text-amber-200 font-medium"
                  >
                    {{ $t("Minimum purchase amount is $10") }}
                  </span>
                </div>
                <div class="text-xs text-amber-700 dark:text-amber-300 mt-1">
                  {{
                    $t(
                      "Please add more resources to reach the minimum purchase amount."
                    )
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </UPricingPlan>
  </div>
</template>
